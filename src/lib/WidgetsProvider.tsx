import React, { createContext, useContext, useState, useCallback } from 'react';
import type { Widget, WidgetCategory, WidgetsContextType, WidgetsProviderProps } from './types';

const WidgetsContext = createContext<WidgetsContextType | undefined>(undefined);

export const useWidgets = (): WidgetsContextType => {
  const context = useContext(WidgetsContext);
  if (!context) {
    throw new Error('useWidgets must be used within a WidgetsProvider');
  }
  return context;
};

export const WidgetsProvider: React.FC<WidgetsProviderProps> = ({
  children,
  initialWidgets = [],
  initialCategories = [],
}) => {
  const [widgets, setWidgets] = useState<Widget[]>(initialWidgets);
  const [categories] = useState<WidgetCategory[]>(initialCategories);

  const addWidget = useCallback((widget: Widget) => {
    setWidgets(prev => {
      // Check if widget already exists
      if (prev.some(w => w.id === widget.id)) {
        console.warn(`Widget with id "${widget.id}" already exists`);
        return prev;
      }
      return [...prev, widget];
    });
  }, []);

  const removeWidget = useCallback((widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  }, []);

  const getWidgetsByCategory = useCallback((categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    return category ? category.widgets : [];
  }, [categories]);

  const contextValue: WidgetsContextType = {
    widgets,
    categories,
    addWidget,
    removeWidget,
    getWidgetsByCategory,
  };

  return (
    <WidgetsContext.Provider value={contextValue}>
      {children}
    </WidgetsContext.Provider>
  );
};

export default WidgetsProvider;
